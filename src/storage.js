const fs = require("fs").promises;
const path = require("path");

class Storage {
  constructor(dataDir = "./data") {
    this.dataDir = dataDir;
    this.ensureDataDir();
  }

  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error("Error creating data directory:", error);
    }
  }

  getStorageFile(siteName) {
    return path.join(
      this.dataDir,
      `${siteName.toLowerCase().replace(/\s+/g, "_")}.json`
    );
  }

  async getSeenItems(siteName) {
    try {
      const filePath = this.getStorageFile(siteName);
      const data = await fs.readFile(filePath, "utf8");
      return JSON.parse(data);
    } catch (error) {
      if (error.code === "ENOENT") {
        return []; // File doesn't exist, return empty array
      }
      console.error(`Error reading seen items for ${siteName}:`, error);
      return [];
    }
  }

  async saveSeenItems(siteName, items) {
    try {
      const filePath = this.getStorageFile(siteName);
      await fs.writeFile(filePath, JSON.stringify(items, null, 2));
    } catch (error) {
      console.error(`Error saving seen items for ${siteName}:`, error);
    }
  }

  async addSeenItem(siteName, item) {
    const seenItems = await this.getSeenItems(siteName);

    // Create a unique identifier for the item
    const itemId = this.generateItemId(item);

    // Check if item already exists
    if (!seenItems.find((seen) => seen.id === itemId)) {
      seenItems.push({
        id: itemId,
        title: item.title,
        url: item.url,
        price: item.price,
        image: item.image,
        source: item.source,
        domain: item.domain,
        timestamp: new Date().toISOString(),
      });

      // Keep only the last 1000 items to prevent file from growing too large
      if (seenItems.length > 1000) {
        seenItems.splice(0, seenItems.length - 1000);
      }

      await this.saveSeenItems(siteName, seenItems);
    }
  }

  generateItemId(item) {
    // Generate a simple hash-like ID from title and URL
    const str = `${item.title || ""}${item.url || ""}`;
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString();
  }

  async isNewItem(siteName, item) {
    const seenItems = await this.getSeenItems(siteName);
    const itemId = this.generateItemId(item);
    return !seenItems.find((seen) => seen.id === itemId);
  }

  getFirstRunFile(siteName) {
    return path.join(
      this.dataDir,
      `${siteName.toLowerCase().replace(/\s+/g, "_")}_first_run.json`
    );
  }

  async isFirstRun(siteName) {
    try {
      const filePath = this.getFirstRunFile(siteName);
      const data = await fs.readFile(filePath, "utf8");
      const firstRunData = JSON.parse(data);
      return !firstRunData.completed;
    } catch (error) {
      if (error.code === "ENOENT") {
        return true; // File doesn't exist, so it's the first run
      }
      console.error(`Error checking first run status for ${siteName}:`, error);
      return true; // Default to first run on error
    }
  }

  async markFirstRunComplete(siteName) {
    try {
      const filePath = this.getFirstRunFile(siteName);
      const firstRunData = {
        completed: true,
        completedAt: new Date().toISOString(),
        siteName: siteName,
      };
      await fs.writeFile(filePath, JSON.stringify(firstRunData, null, 2));
      console.log(`✅ [${siteName}] First run marked as complete`);
    } catch (error) {
      console.error(`Error marking first run complete for ${siteName}:`, error);
    }
  }
}

module.exports = Storage;
