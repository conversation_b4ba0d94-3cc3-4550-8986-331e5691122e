# Website Monitor with Slack Notifications

A powerful Node.js application that monitors websites for new content and sends real-time notifications to Slack. Perfect for tracking real estate listings, news updates, job postings, or any website content changes.

## 🌟 Features

- 🔍 **Smart Website Monitoring**: Automatically detects new content on any website
- 📱 **Slack Integration**: Beautiful, rich notifications sent directly to your Slack channel
- 🤖 **Anti-Bot Protection Bypass**: Advanced browser simulation with human-like behavior
- 🌍 **Proxy Support**: Route requests through different countries to avoid IP blocking
- 💾 **Intelligent Storage**: Tracks seen content to prevent duplicate notifications
- ⏰ **Flexible Scheduling**: Configure check intervals from minutes to hours
- 🛡️ **Robust Error Handling**: Comprehensive error handling with notifications
- 🎯 **Universal Compatibility**: Works with most website structures

## 📋 Prerequisites

Before you begin, make sure you have:

1. **Node.js** (version 14 or higher)
   - Download from [nodejs.org](https://nodejs.org/)
   - Check your version: `node --version`

2. **A Slack workspace** where you can add apps
   - You need admin permissions or ability to add apps

3. **Basic terminal/command line knowledge**
   - How to navigate folders and run commands

## 🚀 Quick Start Guide

### Step 1: Download the Project

If you have the project files, navigate to the project folder in your terminal:

```bash
cd path/to/newsletter
```

### Step 2: Install Required Packages

Run this command to install all necessary dependencies:

```bash
npm install
```

This will install:
- `axios` - For making web requests
- `cheerio` - For parsing website content
- `@slack/webhook` - For Slack notifications
- `puppeteer` - For browser automation
- `node-cron` - For scheduling
- And other supporting packages

### Step 3: Set Up Slack Integration

#### 3.1 Create a Slack App

1. Go to [api.slack.com/apps](https://api.slack.com/apps)
2. Click **"Create New App"**
3. Choose **"From scratch"**
4. Give your app a name (e.g., "Website Monitor")
5. Select your Slack workspace
6. Click **"Create App"**

#### 3.2 Enable Incoming Webhooks

1. In your app settings, click **"Incoming Webhooks"** in the left sidebar
2. Toggle **"Activate Incoming Webhooks"** to **On**
3. Click **"Add New Webhook to Workspace"**
4. Choose the channel where you want notifications (e.g., #general)
5. Click **"Allow"**
6. **Copy the webhook URL** - it looks like:
   ```
   *******************************************************************************
   ```

### Step 4: Configure the Application

#### 4.1 Set Up Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Open the `.env` file in any text editor and update it:
   ```env
   # Replace with your actual Slack webhook URL
   SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

   # Check every 4 hours (240 minutes) - recommended for avoiding anti-bot detection
   CHECK_INTERVAL_MINUTES=240

   # Use browser-based monitoring for protected sites
   USE_BROWSER=true

   # Log level for debugging
   LOG_LEVEL=info

   # Save HTML for debugging (optional)
   DEBUG_HTML=false
   ```

#### 4.2 Configure Websites to Monitor

Edit the `urls.json` file to specify which websites to monitor:

```json
{
  "site": [
    {
      "name": "Chotot Real Estate",
      "url": "https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000"
    },
    {
      "name": "Another Website",
      "url": "https://example.com/listings"
    }
  ]
}
```

**Important Notes:**
- `name`: A friendly name that will appear in Slack notifications
- `url`: The exact URL you want to monitor
- You can add multiple websites to monitor

## 🏃‍♂️ Running the Application

### Start Monitoring

Run this command to start the website monitor:

```bash
npm start
```

### What Happens When You Start

1. **Startup Notification**: You'll receive a Slack message confirming the monitor started
2. **Initial Check**: The app will check all configured websites immediately
3. **Scheduled Monitoring**: It will then check every 4 hours (or your configured interval)
4. **New Content Alerts**: When new items are found, you'll get Slack notifications

### Example Output

```
🚀 Starting Newsletter Monitor...
📧 Slack notifications enabled
⏰ Check interval: 240 minutes
🌐 Using browser-based monitoring (slower but more reliable)
🚀 Startup notification sent
📋 Monitoring 1 sites:
   • Chotot Real Estate: https://www.nhatot.com/...

🔍 Running initial check...
🌐 Starting browser from random location...
👤 Human from random country navigating to: https://www.nhatot.com/...
✅ Found 5 new items on Chotot Real Estate
🆕 Slack notification sent for 5 new items

⏰ Scheduling checks every 240 minutes
✅ Newsletter Monitor is running! Press Ctrl+C to stop.
```

### Stop the Monitor

Press `Ctrl+C` in the terminal to stop the application.

## ⚙️ Configuration Options

### Environment Variables (`.env` file)

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `SLACK_WEBHOOK_URL` | Your Slack webhook URL | *Required* | `https://hooks.slack.com/services/...` |
| `CHECK_INTERVAL_MINUTES` | How often to check websites | 30 | 240 (4 hours) |
| `USE_BROWSER` | Use browser simulation for protected sites | false | true |
| `LOG_LEVEL` | Logging detail level | info | debug, info, warn, error |
| `DEBUG_HTML` | Save page HTML for debugging | false | true |

### Website Configuration (`urls.json`)

```json
{
  "site": [
    {
      "name": "Display Name",
      "url": "https://website-to-monitor.com/page"
    }
  ]
}
```

## 🔧 Advanced Features

### Browser-Based Monitoring

For websites with anti-bot protection (like Cloudflare), enable browser mode:

```env
USE_BROWSER=true
```

This will:
- Open a real browser window
- Simulate human-like behavior (mouse movements, scrolling)
- Wait patiently for challenge pages to complete
- Use random user agents from different countries

### Proxy Support

The application includes proxy support for additional anonymity:

1. Edit `src/browser-monitor.js`
2. Add working proxy servers to the `freeProxies` array:
   ```javascript
   this.freeProxies = [
       'proxy-server:port',
       '123.456.789.123:8080',
   ];
   ```

## 📁 Project Structure

```
newsletter/
├── index.js                 # Main application entry point
├── src/
│   ├── monitor.js          # HTTP-based website monitoring
│   ├── browser-monitor.js  # Browser-based monitoring with anti-bot bypass
│   ├── slack.js           # Slack notification handler
│   └── storage.js         # Content storage and duplicate detection
├── data/                  # Stored seen items (auto-created)
│   └── site_name.json    # Seen items for each site
├── urls.json             # Website configuration
├── .env                  # Environment variables (create from .env.example)
├── .env.example         # Environment template
├── package.json         # Node.js dependencies
└── README.md           # This documentation
```

## 🔍 How It Works

### Content Detection Process

1. **Page Loading**: The app loads the target website
2. **Content Extraction**: Uses intelligent selectors to find items (articles, listings, posts)
3. **Duplicate Detection**: Generates unique IDs for each item based on title and URL
4. **Comparison**: Compares current items with previously seen items
5. **Notification**: Sends Slack alerts for genuinely new content

### Anti-Bot Protection Handling

For protected websites, the app:
- Opens a real browser window (not headless)
- Uses random user agents from different countries
- Simulates human behavior (reading pauses, mouse movements, scrolling)
- Waits patiently for Cloudflare challenges to complete
- Retries with page refreshes if needed

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. "No items found" Message

**Possible Causes:**
- Website structure changed
- Content is loaded dynamically with JavaScript
- Website is blocking requests

**Solutions:**
- Enable browser mode: `USE_BROWSER=true`
- Check if the website loads properly in a regular browser
- Try a different URL or website

#### 2. Slack Notifications Not Working

**Check:**
- Webhook URL is correct and complete
- Slack app has permission to post to the channel
- No typos in the `.env` file

**Test:**
- Try sending a test message to the webhook URL

#### 3. "403 Forbidden" or "Access Denied" Errors

**This means:**
- The website is blocking automated requests
- Your IP might be temporarily blocked

**Solutions:**
- Enable browser mode: `USE_BROWSER=true`
- Increase check interval: `CHECK_INTERVAL_MINUTES=480` (8 hours)
- Add proxy support
- Try accessing the website manually first

#### 4. Browser Crashes or Connection Errors

**Solutions:**
- Restart the application
- Check if you have enough system memory
- Close other browser windows
- Update Chrome/Chromium

#### 5. High Memory Usage

**Causes:**
- Monitoring many websites
- Browser mode uses more memory

**Solutions:**
- Reduce check frequency
- Monitor fewer websites simultaneously
- Restart the application periodically

### Debug Mode

Enable detailed logging for troubleshooting:

```env
LOG_LEVEL=debug
DEBUG_HTML=true
```

This will:
- Show detailed logs of what the app is doing
- Save HTML content to `debug-page.html` for inspection

## 📊 Monitoring Best Practices

### Recommended Settings

1. **Check Interval**: 4+ hours for protected sites
   ```env
   CHECK_INTERVAL_MINUTES=240
   ```

2. **Browser Mode**: For modern websites with protection
   ```env
   USE_BROWSER=true
   ```

3. **Respectful Monitoring**: Don't check too frequently
   - Avoid intervals less than 30 minutes
   - Consider the website's server load

### Website Compatibility

**Works Best With:**
- News websites
- Blog sites
- E-commerce product pages
- Real estate listings
- Job boards

**May Need Browser Mode:**
- Sites with Cloudflare protection
- Sites requiring JavaScript
- Modern single-page applications

## 🤝 Support and Contributing

### Getting Help

1. Check this documentation first
2. Look at the console output for error messages
3. Enable debug mode for detailed logs
4. Check if the website works in a regular browser

### Contributing

Feel free to:
- Report bugs
- Suggest new features
- Submit improvements
- Share working proxy lists

## 📄 License

ISC License - Feel free to use and modify for your needs.

---

## 🎯 Quick Reference Commands

```bash
# Install dependencies
npm install

# Start monitoring
npm start

# Stop monitoring
Ctrl+C

# Check Node.js version
node --version

# View logs in real-time
tail -f debug-page.html  # If DEBUG_HTML=true
```

**Need help?** Check the troubleshooting section above or review the console output for specific error messages.

## 📱 Example Slack Notifications

When the monitor finds new content, you'll receive notifications like this:

### Startup Notification
```
🚀 Website Monitor Started
The website monitoring service has started successfully!
Started at 12/15/2024, 2:30:45 PM
```

### New Content Found
```
🔔 New items on Chotot Real Estate
Found 3 new items:
• New 2BR apartment in District 7 - 6,500,000 VND
• Modern studio near Lotte Mart - 5,800,000 VND
• Spacious 1BR with balcony - 7,200,000 VND
Checked at 12/15/2024, 6:30:45 PM
```

### Error Notification
```
⚠️ Monitoring Error
Failed to check Chotot Real Estate
Error: Access denied (403) - Website may be blocking automated requests
Error occurred at 12/15/2024, 10:30:45 PM
```

## 🔐 Security Notes

- Keep your `.env` file secure and never commit it to version control
- Your Slack webhook URL should be kept private
- The application doesn't store any personal data
- All website content is processed locally on your machine

## 🌍 International Usage

The application works worldwide and includes:
- Multi-language support for website content
- International user agents
- Timezone-aware notifications
- Support for various website structures globally

**Happy Monitoring!** 🎉
